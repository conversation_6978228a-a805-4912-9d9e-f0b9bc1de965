<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.4.4</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>

  <groupId>com.thy.qa</groupId>
  <artifactId>pg-api-test-automation</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <name>pg-api-test-automation</name>
  <description>Payment Gateway API Test Automation</description>

  <properties>
    <java.version>21</java.version>
    <cucumber.version>7.22.0</cucumber.version>
    <junit-jupiter.version>5.13.0</junit-jupiter.version>
    <junit-platform-suite.version>1.13.0</junit-platform-suite.version>
    <rest-assured.version>5.5.1</rest-assured.version>
    <jackson.version>2.18.3</jackson.version>
    <lombok.version>1.18.38</lombok.version>
    <spring.cloud.version>4.2.1</spring.cloud.version>
    <rp.logback.version>5.2.3</rp.logback.version>
    <rp.agent-java-cucumber7.version>5.3.1</rp.agent-java-cucumber7.version>
    <commons-text.version>1.13.1</commons-text.version>
  </properties>

  <dependencies>
    <!-- Spring Boot -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- Logback Dependencies -->
    <dependency>
      <groupId>com.epam.reportportal</groupId>
      <artifactId>logger-java-logback</artifactId>
      <version>${rp.logback.version}</version>
    </dependency>

    <!-- Spring Cloud Consul Config-->
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-consul-config</artifactId>
      <version>${spring.cloud.version}</version>
    </dependency>

    <!-- REST Assured -->
    <dependency>
      <groupId>io.rest-assured</groupId>
      <artifactId>rest-assured</artifactId>
      <version>${rest-assured.version}</version>
    </dependency>

    <!-- XML Processing -->
    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-xml</artifactId>
      <version>${jackson.version}</version>
    </dependency>

    <!-- Lombok -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
      <scope>provided</scope>
    </dependency>

    <!-- Cucumber -->
    <dependency>
      <groupId>io.cucumber</groupId>
      <artifactId>cucumber-java</artifactId>
      <version>${cucumber.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.cucumber</groupId>
      <artifactId>cucumber-spring</artifactId>
      <version>${cucumber.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.cucumber</groupId>
      <artifactId>cucumber-junit-platform-engine</artifactId>
      <version>${cucumber.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.epam.reportportal</groupId>
      <artifactId>agent-java-cucumber7</artifactId>
      <version>${rp.agent-java-cucumber7.version}</version>
    </dependency>

    <!-- JUnit 5 -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>${junit-jupiter.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-suite</artifactId>
      <version>${junit-platform-suite.version}</version>
      <scope>test</scope>
    </dependency>

    <!-- Testing utilities -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-text</artifactId>
      <version>${commons-text.version}</version>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.14.0</version>
        <configuration>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <plugin>
        <groupId>app.getxray</groupId>
        <artifactId>xray-maven-plugin</artifactId>
        <version>0.8.0</version>
        <configuration>
          <cloud>false</cloud>
          <jiraBaseUrl>https://jira.thy.com</jiraBaseUrl>
          <projectKey>ODETP</projectKey>
          <jiraToken>NDc3NjQyMTg3MTMwOgvCFthdkFJuvc3qQwGBZNFem1C6</jiraToken>
          <inputFeatures>${project.basedir}/src/test/resources/features/New.feature</inputFeatures>
          <issueKeys>ODETP-11558</issueKeys>
          <outputDir>${project.basedir}</outputDir>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
