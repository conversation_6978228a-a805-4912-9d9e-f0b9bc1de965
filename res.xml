<App2PGServiceRequestMessage xmlns="schema.pg.thy.com" xmlns:ns2="http://thy.com/paymentgateway/paypal">
  <Language>en</Language>
  <ApplicationName>IBS_STAGING</ApplicationName>
  <ClientApplicationVersion>node-fetch</ClientApplicationVersion>
  <SubChannel>WEB3</SubChannel>
  <ClientCode>IBS_STAGING</ClientCode>
  <ClientIP>*************</ClientIP>
  <ClientOS>Other</ClientOS>
  <DeviceType>Other</DeviceType>
  <ClientBrowserDetails>
    <SessionID>Mjk5MzgzMjQtNzk3ZC00NTM2LThiZmItOWI0ZWViZDUwNzZi</SessionID>
    <AcceptHeader>text/html,application/xhtml+xml,application/xml;q=0.9,*/*; q=0.8</AcceptHeader>
    <UserAgentHeader>node-fetch</UserAgentHeader>
    <WindowSize>500x600</WindowSize>
    <TimezoneOffSet>0</TimezoneOffSet>
    <ColorDepth>0</ColorDepth>
    <JavaEnabled>false</JavaEnabled>
  </ClientBrowserDetails>
  <OrderDetails>
    <SOR>
      <signInCode xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <CityCode>IST</CityCode>
      <OfficeNumber>1103</OfficeNumber>
      <ReportNumber xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <GroupNumber xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <ItemNumber xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <TaxIdNumber xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <AgentCountryCode xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
    </SOR>
    <TrnxID>01JYGTAF9XCXRA6WMWRDV40JW9</TrnxID>
    <OrderID>E3I9AE</OrderID>
    <NPhaseId>1750763650365</NPhaseId>
    <TransactionType>CloseAuth</TransactionType>
    <TotalAmount>1.00</TotalAmount>
    <Currency>EUR</Currency>
    <multiplePayment>false</multiplePayment>
    <PayByLink xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
    <Is3DVerificationUsed xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
  </OrderDetails>
  <ApiCallRequest>
    <paymentInstrument>UnionPay</paymentInstrument>
    <TDSParameters>
      <EncodedPan xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <CardHolderPresentCode xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <SecurityLevel xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <PayerAuthCode xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <PayerSecurityLevel xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <MdStatus xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <MerchantPacket xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <BankPacket xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <Sign xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <WorldPayTDSParameters xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <extraParameters>
        <Parameter>
          <Name>ReturnOid</Name>
          <Value>E3I9AE</Value>
        </Parameter>
        <Parameter>
          <Name>ACQAUTHID</Name>
          <Value>000000</Value>
        </Parameter>
        <Parameter>
          <Name>hashAlgorithm</Name>
          <Value>ver3</Value>
        </Parameter>
        <Parameter>
          <Name>EXTRA.HOSTDATE</Name>
          <Value>0624-141608</Value>
        </Parameter>
        <Parameter>
          <Name>islemtipi</Name>
          <Value>Auth</Value>
        </Parameter>
        <Parameter>
          <Name>url</Name>
          <Value>https://istest.asseco-see.com.tr/fim/est3dgate</Value>
        </Parameter>
        <Parameter>
          <Name>lang</Name>
          <Value>en</Value>
        </Parameter>
        <Parameter>
          <Name>maskedCreditCard</Name>
          <Value>8171 99** **** 0000</Value>
        </Parameter>
        <Parameter>
          <Name>amount</Name>
          <Value>1</Value>
        </Parameter>
        <Parameter>
          <Name>MaskedPan</Name>
          <Value>817199***0000</Value>
        </Parameter>
        <Parameter>
          <Name>paymentType</Name>
          <Value>UPOPV5</Value>
        </Parameter>
        <Parameter>
          <Name>clientIp</Name>
          <Value>************</Value>
        </Parameter>
        <Parameter>
          <Name>cardHolderName</Name>
          <Value>Gundogan Basaga</Value>
        </Parameter>
        <Parameter>
          <Name>okUrl</Name>
          <Value>https://commonpay-fe-commonpay-test.apps.ocpnonprod.thy.com/callback/apm/unionpay/ok?token=95713e51-ffe3-417b-92e6-997f873d16eb</Value>
        </Parameter>
        <Parameter>
          <Name>TRANUID</Name>
          <Value>25175OOfH07036081</Value>
        </Parameter>
        <Parameter>
          <Name>ACQRRN</Name>
          <Value>************</Value>
        </Parameter>
        <Parameter>
          <Name>ShipToStateProv</Name>
          <Value/>
        </Parameter>
        <Parameter>
          <Name>ProcReturnCode</Name>
          <Value>00</Value>
        </Parameter>
        <Parameter>
          <Name>clientId</Name>
          <Value>************</Value>
        </Parameter>
        <Parameter>
          <Name>email</Name>
          <Value><EMAIL></Value>
        </Parameter>
        <Parameter>
          <Name>TransId</Name>
          <Value>25175OOfH07036081</Value>
        </Parameter>
        <Parameter>
          <Name>EXTRA.TRXDATE</Name>
          <Value>20250624 14:16:08</Value>
        </Parameter>
        <Parameter>
          <Name>PHONENO</Name>
          <Value>5004003020</Value>
        </Parameter>
        <Parameter>
          <Name>storetype</Name>
          <Value>3d</Value>
        </Parameter>
        <Parameter>
          <Name>Response</Name>
          <Value>Approved</Value>
        </Parameter>
        <Parameter>
          <Name>SettleId</Name>
          <Value>7028</Value>
        </Parameter>
        <Parameter>
          <Name>BillToStateProv</Name>
          <Value/>
        </Parameter>
        <Parameter>
          <Name>ACQRESPDETAIL</Name>
          <Value>Success[0000000]</Value>
        </Parameter>
        <Parameter>
          <Name>HostRefNum</Name>
          <Value>942506241914314721528</Value>
        </Parameter>
        <Parameter>
          <Name>AuthCode</Name>
          <Value>472152</Value>
        </Parameter>
        <Parameter>
          <Name>failUrl</Name>
          <Value>https://commonpay-fe-commonpay-test.apps.ocpnonprod.thy.com/callback/apm/unionpay/fail?token=95713e51-ffe3-417b-92e6-997f873d16eb</Value>
        </Parameter>
        <Parameter>
          <Name>xid</Name>
          <Value>d9JVv7/V13nKeoUrtQpRK71YyXA=</Value>
        </Parameter>
        <Parameter>
          <Name>fmsssntkn</Name>
          <Value>imWPa9Iudy8iZGatApQ1ZdItI6Z0MqW0YHfxybJ5jEHfb9yPSl7oDqvtWHGP0703</Value>
        </Parameter>
        <Parameter>
          <Name>encoding</Name>
          <Value>UTF-8</Value>
        </Parameter>
        <Parameter>
          <Name>isOrigin</Name>
          <Value>true</Value>
        </Parameter>
        <Parameter>
          <Name>currency</Name>
          <Value>978</Value>
        </Parameter>
        <Parameter>
          <Name>oid</Name>
          <Value>E3I9AE</Value>
        </Parameter>
        <Parameter>
          <Name>ACQSTAN</Name>
          <Value>472152</Value>
        </Parameter>
        <Parameter>
          <Name>clientid</Name>
          <Value>************</Value>
        </Parameter>
        <Parameter>
          <Name>txnType</Name>
          <Value>PURCHASE</Value>
        </Parameter>
        <Parameter>
          <Name>ccode</Name>
          <Value>TR</Value>
        </Parameter>
        <Parameter>
          <Name>HASH</Name>
          <Value>MtKG5c+C1A1zsoCvRxM7xl5g38k3c0BhuTkLQCX4Zw7ySu7wtQ8jt/pyeq29l9NEFqa/gQCGOkGB7MG1q+LAHw==</Value>
        </Parameter>
        <Parameter>
          <Name>rnd</Name>
          <Value>NdUXxrBwBUJhq5Ok1CI7</Value>
        </Parameter>
      </extraParameters>
      <ThreeDSecureResultDescription xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
    </TDSParameters>
    <ClosePayment>
      <Status>OK</Status>
      <ReturnCode>00</ReturnCode>
      <TransactionId>25175OOfH07036081</TransactionId>
    </ClosePayment>
  </ApiCallRequest>
  <TravelInformatonDetails>
    <OriginCountry2Digits>TR</OriginCountry2Digits>
    <DestinationCountry2Digits>TR</DestinationCountry2Digits>
    <JourneyType>OneWay</JourneyType>
    <FlightType>Domestic</FlightType>
    <ThrdPartyBooking>false</ThrdPartyBooking>
    <EtktIndicator>false</EtktIndicator>
    <PnrNumber>615243</PnrNumber>
    <PnrCreationDate>20251424</PnrCreationDate>
    <SalesOfficeInformation>
      <signInCode xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <CityCode>IST</CityCode>
      <OfficeNumber>1103</OfficeNumber>
      <ReportNumber xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <GroupNumber xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <ItemNumber xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <TaxIdNumber xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      <AgentCountryCode xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
    </SalesOfficeInformation>
    <PassengerList>
      <Passenger>
        <Index>1</Index>
        <Title>MR</Title>
        <Type>Adult</Type>
        <GivenName>Gündoğan</GivenName>
        <FamilyName>Başağa</FamilyName>
        <EmailAddress><EMAIL></EmailAddress>
        <PhoneNumber>5004003020</PhoneNumber>
        <TicketAmount xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
        <DocumentNumber xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
        <PassengerMilesInfo xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
        <Documents>
          <Document>
            <TransactionCode>TKTT</TransactionCode>
          </Document>
        </Documents>
      </Passenger>
    </PassengerList>
    <SegmentList>
      <Segment>
        <Index>1</Index>
        <OriginAirport>SAW</OriginAirport>
        <OriginAirportGmt>Z</OriginAirportGmt>
        <DestinationAirport>NOP</DestinationAirport>
        <Carrier>TK</Carrier>
        <FlightNumber>5712</FlightNumber>
        <FlightDate>20250625</FlightDate>
        <FlightDepartureTime>11:14</FlightDepartureTime>
        <FlightArrivalDate>20250625</FlightArrivalDate>
        <FlightArrivalTime>11:14</FlightArrivalTime>
        <ClassCode>C</ClassCode>
        <Cabin>Economy</Cabin>
        <FareBasis>C</FareBasis>
        <BaggageAllowance xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
        <FareBasisTicketDesignator xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
        <FareComponentPricedPassengerTypeCode xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
        <FlightBookingStatus xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
        <FrequentFlyerReference xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
        <NotValidAfter xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
        <NotValidBefore xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
        <ReservationBooking xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
        <SoldPassengerCabin xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
        <StopoverCode xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
      </Segment>
    </SegmentList>
  </TravelInformatonDetails>
  <AgencyCreditCardUsed>false</AgencyCreditCardUsed>
</App2PGServiceRequestMessage>