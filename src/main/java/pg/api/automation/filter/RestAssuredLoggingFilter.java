package pg.api.automation.filter;

import io.restassured.filter.Filter;
import io.restassured.filter.FilterContext;
import io.restassured.response.Response;
import io.restassured.specification.FilterableRequestSpecification;
import io.restassured.specification.FilterableResponseSpecification;
import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RestAssuredLoggingFilter implements Filter {

    private static final String REQUEST_HEADER = "\n--------------------REQUEST--------------------\n";
    private static final String RESPONSE_HEADER = "\n--------------------RESPONSE-------------------\n";
    private static final String FOOTER = "-----------------------------------------------";
    private static final String EMPTY_BODY = "Body: <empty>\n";

    @Override
    public Response filter(FilterableRequestSpecification requestSpec,
        FilterableResponseSpecification responseSpec, FilterContext ctx) {
        // Log the request details before sending
        logRequest(requestSpec);

        // Execute the request and get the response
        long startTime = System.currentTimeMillis();
        Response response = ctx.next(requestSpec, responseSpec);
        long responseTime = System.currentTimeMillis() - startTime;

        // Log the response details
        logResponse(response, responseTime);
        return response;
    }

    private void logRequest(FilterableRequestSpecification requestSpec) {
        StringBuilder requestLog = new StringBuilder(REQUEST_HEADER);
        appendBasicRequestInfo(requestLog, requestSpec);
        appendHeaders(requestLog, requestSpec.getHeaders());
        appendCookies(requestLog, requestSpec);
        appendParameters(requestLog, requestSpec);
        appendBody(requestLog, requestSpec);
        requestLog.append(FOOTER);
        log.info(requestLog.toString());
    }

    private void logResponse(Response response, long responseTime) {
        StringBuilder responseLog = new StringBuilder(RESPONSE_HEADER);
        appendBasicResponseInfo(responseLog, response, responseTime);
        appendHeaders(responseLog, response.getHeaders());
        appendResponseCookies(responseLog, response);
        appendResponseBody(responseLog, response);
        responseLog.append(FOOTER);
        log.info(responseLog.toString());
    }

    private void appendBasicRequestInfo(StringBuilder builder,
        FilterableRequestSpecification requestSpec) {
        builder.append("Request Time: ")
            .append(Instant.now(Clock.system(ZoneId.systemDefault())).toString()).append(" ms\n");
        builder.append("HTTP Method: ").append(requestSpec.getMethod()).append("\n");
        builder.append("URI: ").append(requestSpec.getURI()).append("\n");
    }

    private void appendBasicResponseInfo(StringBuilder builder, Response response,
        long responseTime) {
        builder.append("Status Code: ").append(response.getStatusCode()).append("\n");
        builder.append("Status Line: ").append(response.getStatusLine()).append("\n");
        builder.append("Response Time: ").append(responseTime).append(" ms\n");
    }

    private void appendHeaders(StringBuilder builder,
        Iterable<io.restassured.http.Header> headers) {
        builder.append("Headers: {\n");
        headers.forEach(header -> builder.append("  ").append(header.getName()).append(": ")
            .append(header.getValue()).append("\n"));
        builder.append("}\n");
    }

    private void appendCookies(StringBuilder builder, FilterableRequestSpecification requestSpec) {
        if (requestSpec.getCookies().exist()) {
            builder.append("Cookies: {\n");
            requestSpec.getCookies().forEach(
                cookie -> builder.append("  ").append(cookie.getName()).append(": ")
                    .append(cookie.getValue()).append("\n"));
            builder.append("}\n");
        }
    }

    private void appendResponseCookies(StringBuilder builder, Response response) {
        if (!response.getCookies().isEmpty()) {
            builder.append("Cookies: {\n");
            response.getCookies().forEach(
                (name, value) -> builder.append("  ").append(name).append(": ").append(value)
                    .append("\n"));
            builder.append("}\n");
        }
    }

    private void appendParameters(StringBuilder builder,
        FilterableRequestSpecification requestSpec) {
        appendMapParameters(builder, "Path Parameters", requestSpec.getPathParams());
        appendMapParameters(builder, "Query Parameters", requestSpec.getQueryParams());
        appendMapParameters(builder, "Form Parameters", requestSpec.getFormParams());
        appendMultiPartParameters(builder, requestSpec);
    }

    private void appendMapParameters(StringBuilder builder, String paramType,
        Map<String, ?> params) {
        if (params != null && !params.isEmpty()) {
            builder.append(paramType).append(": {\n");
            params.forEach(
                (key, value) -> builder.append("  ").append(key).append(": ").append(value)
                    .append("\n"));
            builder.append("}\n");
        }
    }

    private void appendMultiPartParameters(StringBuilder builder,
        FilterableRequestSpecification requestSpec) {
        if (requestSpec.getMultiPartParams() != null && !requestSpec.getMultiPartParams()
            .isEmpty()) {
            builder.append("MultiPart Form Data: {\n");
            requestSpec.getMultiPartParams().forEach(param -> {
                builder.append("\tPart Name: ").append(param.getControlName());
                if (param.getFileName() != null) {
                    builder.append(", Filename: ").append(param.getFileName());
                }
                if (param.getMimeType() != null) {
                    builder.append(", Content-Type: ").append(param.getMimeType());
                }
                builder.append(", Content: ").append("\n\t\t").append(param.getContent());
                builder.append("\n");
            });
            builder.append("}\n");
        }
    }

    private void appendBody(StringBuilder builder, FilterableRequestSpecification requestSpec) {
        if (requestSpec.getBody() != null) {
            builder.append("Body: ").append(requestSpec.getBody().toString()).append("\n");
        } else {
            builder.append(EMPTY_BODY);
        }
    }

    private void appendResponseBody(StringBuilder builder, Response response) {
        String bodyString = response.getBody().asPrettyString();
        if (!bodyString.isEmpty()) {
            builder.append("Body: ").append(bodyString).append("\n");
        } else {
            builder.append(EMPTY_BODY);
        }
    }
}
