package pg.api.automation.util;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for XML processing
 */
@Slf4j
public class XmlUtils {

    /**
     * Shared XmlMapper instance that can be reused across threads
     */
    private static final XmlMapper XML_MAPPER = createConfiguredXmlMapper();

    private XmlUtils() {
    }

    /**
     * Creates and configures a new XmlMapper instance
     *
     * @return Configured XmlMapper instance
     */
    private static XmlMapper createConfiguredXmlMapper() {
        XmlMapper xmlMapper = new XmlMapper();
        xmlMapper.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE);
        xmlMapper.setSerializationInclusion(Include.NON_EMPTY);
        return xmlMapper;
    }

    /**
     * Converts an object to its XML representation
     *
     * @param <T>    The type of object to serialize
     * @param object The object to serialize
     * @return XML string representation
     */
    public static <T> String toXml(T object) {
        try {
            return XML_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (Exception e) {
            log.error("Error converting object to XML: {}", e.getMessage(), e);
            throw new RuntimeException("Error converting object to XML", e);
        }
    }

    /**
     * Parses an XML string into an object
     *
     * @param <T>       The type to deserialize into
     * @param xml       The XML string to parse
     * @param valueType The class of the object to deserialize into
     * @return The deserialized object
     */
    public static <T> T fromXml(String xml, Class<T> valueType) {
        try {
            return XML_MAPPER.readValue(xml, valueType);
        } catch (Exception e) {
            log.error("Error parsing XML to object: {}", e.getMessage(), e);
            throw new RuntimeException("Error parsing XML to object", e);
        }
    }
}
