package pg.api.automation.converter;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import pg.api.automation.model.consul.Profile;
import pg.api.automation.model.consul.TKFlightSegment;

@Component("consul")
public class ConsulConverter {

    private final ObjectMapper objectMapper;

    @Value("${segments.tk}")
    private String segmentsTk;

    @Value("${profiles}")
    private String profiles;

    @Autowired
    public ConsulConverter(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @SneakyThrows
    public List<TKFlightSegment> flightSegments() {
        return objectMapper.readValue(segmentsTk, objectMapper.getTypeFactory()
            .constructCollectionType(List.class, TKFlightSegment.class));
    }

    @SneakyThrows
    public List<Profile> profiles() {
        return objectMapper.readValue(profiles,
            objectMapper.getTypeFactory().constructCollectionType(List.class, Profile.class));
    }
}
