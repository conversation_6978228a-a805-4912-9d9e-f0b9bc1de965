package pg.api.automation.layout;

import ch.qos.logback.classic.pattern.MessageConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import org.apache.commons.text.StringEscapeUtils;

public class HtmlContentEncoderMessageConverter extends MessageConverter {

    @Override
    public String convert(ILoggingEvent event) {
        return StringEscapeUtils.escapeHtml4(super.convert(event));
    }

}
