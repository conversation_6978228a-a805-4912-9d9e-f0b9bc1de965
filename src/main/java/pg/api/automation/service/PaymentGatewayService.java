package pg.api.automation.service;

import io.restassured.response.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pg.api.automation.model.GeneratedOrderIDRequest;
import pg.api.automation.model.PaymentGatewayRequest;
import pg.api.automation.util.XmlUtils;

/**
 * Service class for Payment Gateway operations
 */
@Service
public class PaymentGatewayService {

    private static final String API_CALL_PAYMENT_PATH = "api-call-payment";
    private static final String GENERATE_ORDER_ID_PATH = "generate-order-id";
    private static final String XML_PARAM_NAME = "PG_XML";

    private final ApiService apiService;

    @Autowired
    public PaymentGatewayService(ApiService apiService) {
        this.apiService = apiService;
    }


    public Response performPaymentRequest(PaymentGatewayRequest request) {
        return apiService.postXmlContent(API_CALL_PAYMENT_PATH, XmlUtils.toXml(request),
            XML_PARAM_NAME);
    }

    public Response generateOrderId(GeneratedOrderIDRequest request) {
        return apiService.postXmlContent(GENERATE_ORDER_ID_PATH, XmlUtils.toXml(request),
            XML_PARAM_NAME);
    }
} 
