package pg.api.automation.service;

import io.restassured.RestAssured;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pg.api.automation.config.ApiConfig;

@Service
public class ApiService {

    private final ApiConfig apiConfig;

    @Autowired
    public ApiService(ApiConfig apiConfig) {
        this.apiConfig = apiConfig;
    }

    /**
     * Makes a POST request to the specified API endpoint with form data
     *
     * @param pathKey    The path key from configuration
     * @param formParams Form parameters to include in the request
     * @return The Response object from the API
     */
    public Response postFormData(String pathKey, Map<String, String> formParams) {
        RequestSpecification request = RestAssured.given().contentType("multipart/form-data");

        formParams.forEach(request::multiPart);

        return request.when().post(apiConfig.getFullUrl(pathKey)).then().extract().response();
    }

    /**
     * Makes a POST request with XML content to the specified API endpoint
     *
     * @param pathKey       The path key from configuration
     * @param xmlContent    The XML content as a string
     * @param formParamName The form parameter name to use for the XML content
     * @return The Response object from the API
     */
    public Response postXmlContent(String pathKey, String xmlContent, String formParamName) {
        return RestAssured.given().contentType("multipart/form-data")
            .multiPart(formParamName, xmlContent).when().post(apiConfig.getFullUrl(pathKey));
    }

    /**
     * Makes a POST request with JSON content to the specified API endpoint
     *
     * @param pathKey     The path key from configuration
     * @param jsonContent The JSON content as a string
     * @return The Response object from the API
     */
    public Response postJsonContent(String pathKey, String jsonContent) {
        return RestAssured.given().contentType("application/json").body(jsonContent).when()
            .post(apiConfig.getFullUrl(pathKey)).then().extract().response();
    }
} 