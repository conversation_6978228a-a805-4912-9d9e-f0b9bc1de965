package pg.api.automation.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Getter;

public class GeneratedOrderIDRequest {

    @JacksonXmlProperty(isAttribute = true, localName = "xmlns")
    private final String xmlNamespace = "schema.pg.thy.com";

    @Getter
    private final String applicationName;

    public GeneratedOrderIDRequest(String applicationName) {
        this.applicationName = applicationName;
    }

}
