package pg.api.automation.model;

import java.util.Arrays;

public enum TransactionType {
    TDS_ADDRESS_LOOKUP("TdsAddressLookup"), FRAUD_CHECK("FraudCheck"), SALE("Sale");

    private final String type;

    TransactionType(String type) {
        this.type = type;
    }

    public String type() {
        return type;
    }

    public static TransactionType fromValue(String value) {
        return Arrays.stream(TransactionType.values()).filter(t -> t.type.equals(value))
            .findFirst().orElseThrow();
    }
}
