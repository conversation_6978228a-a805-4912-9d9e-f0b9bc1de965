package pg.api.automation.model;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import pg.api.automation.model.component.ApiCallRequest;
import pg.api.automation.model.component.CardHolder;
import pg.api.automation.model.component.ClientBrowserDetails;
import pg.api.automation.model.component.ClientInfo;
import pg.api.automation.model.component.FlightSegment;
import pg.api.automation.model.component.FraudDetectionInformationRequest;
import pg.api.automation.model.component.OrderDetails;
import pg.api.automation.model.component.Passenger;
import pg.api.automation.model.component.TravelInformationDetails;
import pg.api.automation.model.component.SalesOfficeInformation;

/**
 * Base class for payment gateway requests
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JacksonXmlRootElement(localName = "App2PGServiceRequestMessage")
public class PaymentGatewayRequest {

    @JacksonXmlProperty(isAttribute = true, localName = "xmlns")
    private final String xmlNamespace = "schema.pg.thy.com";

    private String applicationName;

    @Builder.Default
    @JsonUnwrapped
    private ClientInfo clientInfo = createDefaultClientInfo();

    @Builder.Default
    private ClientBrowserDetails clientBrowserDetails = createDefaultBrowserDetails();

    private OrderDetails orderDetails;

    private ApiCallRequest apiCallRequest;

    @Builder.Default
    private FraudDetectionInformationRequest fraudDetectionInformationRequest = createDefaultFraudDetectionInfo();

    @Builder.Default
    @JacksonXmlProperty(localName = "TravelInformatonDetails")
    private TravelInformationDetails travelInformationDetails = createDefaultTravelInformationDetails();

    public static String generateNPhaseId() {
        LocalDateTime now = LocalDateTime.now();
        String dateTimePart = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String randomPart = String.format("%06d", (int) (Math.random() * 1000000));
        return dateTimePart + randomPart;
    }

    public static ClientInfo createDefaultClientInfo() {
        return new ClientInfo("en", builder().applicationName,
            "CHROME13, 135.0.0.0", "WEB3", "WEB3",
            "46.31.118.93", "Windows", "Computer", "TR", "ANY");
    }

    public static ClientBrowserDetails createDefaultBrowserDetails() {
        return new ClientBrowserDetails(generateSessionId(), "application/json",
            "Computer, Microsoft Corporation, Windows, CHROME13, 135.0.0.0",
            "https://nuat.turkishairlines.com/com.thy.web.online.ibs/ibs/payment/threeDSuccess?cId="
                + generateDeviceFingerprintID(),
            "250x400", "0", "0", false, "en_XX");
    }

    public static FraudDetectionInformationRequest createDefaultFraudDetectionInfo() {
        return new FraudDetectionInformationRequest(
            generateDeviceFingerprintID(), false);
    }

    public static TravelInformationDetails createDefaultTravelInformationDetails() {
        return new TravelInformationDetails("TR", "TR", "OneWay", "Domestic", true, false, "RCNTC6",
            LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")), new SalesOfficeInformation("4662"),
            createDefaultCardHolder(), createDefaultPassengers(),
            createDefaultFlightSegments());
    }

    private static CardHolder createDefaultCardHolder() {
        return new CardHolder("TEST", "CARDHOLDER", "<EMAIL>", "905312323232",
            "1295 TEST STREET", "SUITE 100", "ISTANBUL", "IST", "34000", "TR", "TUR");
    }

    private static List<Passenger> createDefaultPassengers() {
        return List.of(new Passenger(1, "MR", "Adult", "Test", "Passenger", "", ""));
    }

    private static List<FlightSegment> createDefaultFlightSegments() {
        String flightDate = getFutureDate(15);
        return List.of(
            new FlightSegment(1, "AE", "TR", "DXB", "4:00", "IST", "TK", "759", flightDate, "10:30",
                flightDate, "14:35", "Z", "Business", "Z", "NON-STOP"));
    }

    private static String getFutureDate(int daysFromNow) {
        LocalDate date = LocalDate.now().plusDays(daysFromNow);
        return date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    private static String generateSessionId() {
        return "SI-PGAPI-" + generateNowDateTime();
    }

    private static String generateDeviceFingerprintID() {
        return "CTID-PGAPI-" + generateNowDateTime();
    }

    private static String generateNowDateTime() {
        return LocalDateTime.now(ZoneId.systemDefault())
            .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }
}
