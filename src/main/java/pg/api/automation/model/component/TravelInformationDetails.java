package pg.api.automation.model.component;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import java.util.List;

/**
 * Travel information XML representation
 */
public record TravelInformationDetails(String originCountry2Digits,
                                       String destinationCountry2Digits, String journeyType,
                                       String flightType, Boolean thirdPartyBooking,
                                       Boolean etktIndicator, String pnrNumber,
                                       String pnrCreationDate,
                                       SalesOfficeInformation salesOfficeInformation,
                                       CardHolder cardHolder,
                                       @JacksonXmlElementWrapper(localName = "PassengerList") @JacksonXmlProperty(localName = "Passenger") List<Passenger> passengers,
                                       @JacksonXmlElementWrapper(localName = "SegmentList") @JacksonXmlProperty(localName = "Segment") List<FlightSegment> flightSegments) {

}