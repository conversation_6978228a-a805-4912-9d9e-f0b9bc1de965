package pg.api.automation.model.component;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

/**
 * Card holder information XML representation
 */
public record CardHolder(
    String givenName,
    String familyName,
    String email,
    String phoneNumber,
    String streetAddressA,
    String streetAddressB,
    String city,
    String state,
    String postalCode,
    String countryCode,
    @JacksonXmlProperty(localName = "CountryISO3166numeric")
    String countryISO
) {

}