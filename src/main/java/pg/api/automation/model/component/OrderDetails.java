package pg.api.automation.model.component;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

/**
 * Order details XML representation
 */
public record OrderDetails(@JacksonXmlProperty(localName = "SOR") SOR sor,
                           @JacksonXmlProperty(localName = "OrderID") String orderId,
                           @JsonInclude(Include.ALWAYS) String nPhaseId, String transactionType,
                           String totalAmount, String currency, int numberOfInstallment,
                           @JacksonXmlProperty(localName = "multiplePayment") Boolean multiplePayment) {

}