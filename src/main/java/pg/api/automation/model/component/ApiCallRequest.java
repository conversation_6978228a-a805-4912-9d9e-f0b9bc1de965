package pg.api.automation.model.component;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import java.util.List;

/**
 * Model class for payment card details
 */
public record ApiCallRequest(String cardNumber,
                             @JacksonXmlProperty(localName = "ecomPaymentCardExpDateMonth") String expiryMonth,
                             @JacksonXmlProperty(localName = "ecomPaymentCardExpDateYear") String expiryYear,
                             String cvv,
                             @JacksonXmlProperty(localName = "TDSParameters") TDSParameters tdsParameters) {

    public ApiCallRequest(String cardNumber, String expiryMonth, String expiryYear, String cvv) {
        this(cardNumber, expiryMonth, expiryYear, cvv,
            new TDSParameters(List.of(new Parameters(), new Parameters())));
    }

}
