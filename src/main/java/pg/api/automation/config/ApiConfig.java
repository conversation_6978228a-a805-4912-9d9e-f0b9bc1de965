package pg.api.automation.config;

import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "api")
public class ApiConfig {

    private String baseUrl;
    private Map<String, String> paths;

    /**
     * Get the full URL for a specific API endpoint
     *
     * @param pathKey the key of the path in the configuration
     * @return the full URL
     */
    public String getFullUrl(String pathKey) {
        String path = paths.get(pathKey);
        if (path == null) {
            throw new IllegalArgumentException("Path key not found: " + pathKey);
        }
        return baseUrl + path;
    }
} 