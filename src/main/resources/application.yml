# Application configuration
spring:
  config:
    import: optional:consul:consulqa.thy.com:443
  cloud:
    consul:
      config:
        acl-token: Autobots2024
        name: pg-api-automation
      scheme: https

# API Endpoints configuration
api:
  base-url: https://${environment:kurumsaltest}.thy.com
  paths:
    generate-order-id: /PaymentGateway/generateOrderID.do
    api-call-payment: /PaymentGateway/apiCallPayment.do
    generic-info: /PaymentGateway/genericInfoRequest.do
    transaction-query: /PaymentGateway/transactionQueryRequest.do