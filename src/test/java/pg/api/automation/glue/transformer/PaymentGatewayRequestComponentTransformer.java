package pg.api.automation.glue.transformer;

import io.cucumber.java.DataTableType;
import io.cucumber.java.DefaultDataTableCellTransformer;
import io.cucumber.java.DefaultDataTableEntryTransformer;
import io.cucumber.java.DefaultParameterTransformer;
import io.cucumber.java.ParameterType;
import java.lang.reflect.Type;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import pg.api.automation.glue.context.ScenarioRunContext;
import pg.api.automation.model.TransactionType;
import pg.api.automation.model.component.ApiCallRequest;

public class PaymentGatewayRequestComponentTransformer {

    @Autowired
    private ScenarioRunContext context;

    @DefaultParameterTransformer
    @DefaultDataTableEntryTransformer
    @DefaultDataTableCellTransformer
    public Object defaultDataTableEntry(Object fromValue, Type toValueType) {
        System.out.println("heyo");
        if (fromValue instanceof String value) {
            if (value.startsWith("${") && value.endsWith("}")) {
                String key = value.substring(2, value.length() - 1);
                if (context.containsKey(key)) {
                    return context.get(key);
                }
            }
        }
        // Delegate to the related parameter types if not found in context
        return null;
    }

    @ParameterType("TdsAddressLookup|Sale|FraudCheck")
    public TransactionType transactionType(String transactionType) {
        return TransactionType.fromValue(transactionType);
    }

    @ParameterType("\\$\\{context:(.+?)\\}")
    public Object contextPropertyValue(String key) {
        return context.get(key);
    }

    @DataTableType
    public ApiCallRequest transformer(Map<String, String> entry) {
        return new ApiCallRequest(entry.get("cardNumber"), entry.get("expiryMonth"),
            entry.get("expiryYear"), entry.get("cvv"));
    }

}
