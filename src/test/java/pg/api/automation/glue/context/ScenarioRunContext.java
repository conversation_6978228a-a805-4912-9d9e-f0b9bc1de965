package pg.api.automation.glue.context;

import io.cucumber.spring.ScenarioScope;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;
import org.springframework.stereotype.Component;

/**
 * Scenario context class to share state between steps
 */
@Component
@ScenarioScope
public class ScenarioRunContext {

    private final Map<String, Object> contextMap = new HashMap<>();

    public boolean containsKey(String key) {
        return contextMap.containsKey(key);
    }

    /**
     * Stores a value in the context
     *
     * @param key   The key to store the value under
     * @param value The value to store
     */
    public void put(String key, Object value) {
        contextMap.put(key, value);
    }

    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        return (T) contextMap.get(key);
    }

    @SuppressWarnings("unchecked")
    public <T> T getOrDefault(String key, T defaultValue) {
        return (T) contextMap.getOrDefault(key, defaultValue);
    }

    /**
     * Gets a value from the context with type checking
     *
     * @param key   The key to look up
     * @param clazz The expected type of the value
     * @param <T>   The type of the value
     * @return An Optional containing the value, or empty if not found or type mismatch
     */
    public <T> Optional<T> get(String key, Class<T> clazz) {
        return Optional.ofNullable(contextMap.get(key)).filter(clazz::isInstance).map(clazz::cast);
    }

    /**
     * Gets a value from the context and applies a function to it if present
     *
     * @param key      The key to look up
     * @param clazz    The expected type of the value
     * @param function The function to apply to the value if present
     * @param <T>      The type of the value
     * @param <R>      The return type of the function
     * @return An Optional containing the result of applying the function, or empty if value not
     * found
     */
    public <T, R> Optional<R> getAndApply(String key, Class<T> clazz, Function<T, R> function) {
        return get(key, clazz).map(function);
    }

    /**
     * Gets a value from the context and executes a consumer on it if present
     *
     * @param key      The key to look up
     * @param clazz    The expected type of the value
     * @param consumer The consumer to apply to the value if present
     * @param <T>      The type of the value
     */
    public <T> void getAndExecute(String key, Class<T> clazz, Consumer<T> consumer) {
        get(key, clazz).ifPresent(consumer);
    }

    /**
     * Removes a value from the context
     *
     * @param key The key to remove
     * @return The removed value, or null if not found
     */
    public Object remove(String key) {
        return contextMap.remove(key);
    }
} 