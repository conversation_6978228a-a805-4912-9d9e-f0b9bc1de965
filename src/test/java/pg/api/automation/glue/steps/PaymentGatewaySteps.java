package pg.api.automation.glue.steps;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.startsWith;

import io.cucumber.java.en.But;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.restassured.parsing.Parser;
import io.restassured.response.Response;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assumptions;
import org.springframework.beans.factory.annotation.Autowired;
import pg.api.automation.glue.context.ScenarioRunContext;
import pg.api.automation.model.GeneratedOrderIDRequest;
import pg.api.automation.model.PaymentGatewayRequest;
import pg.api.automation.model.TransactionType;
import pg.api.automation.model.component.ApiCallRequest;
import pg.api.automation.model.component.ClientBrowserDetails;
import pg.api.automation.model.component.OrderDetails;
import pg.api.automation.model.component.Passenger;
import pg.api.automation.model.component.SOR;
import pg.api.automation.service.PaymentGatewayService;

@Slf4j
public class PaymentGatewaySteps {

    private static final String REQUEST_KEY = "apiRequest";
    private static final String RESPONSE_KEY = "apiResponse";
    private static final String ORDER_ID_KEY = "orderId";
    private static final String NPHASE_ID_KEY = "nphaseId";
    private static final String PASSENGERS_KEY = "passengers";

    /*@Value("#{consul.flightSegments()}")
    private List<TKFlightSegment> tkFlightSegments;

    @Value("#{consul.profiles()}")
    private List<Profile> profiles;

    @Value("${segments.tk}")
    private List<TKFlightSegment> tkFlightSegments;

    @Value("${profiles}")
    private List<Profile> profiles;*/

    @Autowired
    private PaymentGatewayService paymentGatewayService;

    @Autowired
    private ScenarioRunContext context;

    @Given("I generate an order ID by application name {string}")
    public void iGenerateAnOrderIdFromTheApi(String applicationName) {
        //System.out.println(profiles);
        Response response = paymentGatewayService.generateOrderId(
            new GeneratedOrderIDRequest(applicationName));
        String orderId = response.xmlPath().getString("GeneratedOrderIDResponse.OrderID");

        Assumptions.assumeTrue(response.statusCode() == 200 && StringUtils.isNotBlank(orderId),
            () -> "Failed to generate order ID. Status code: " + response.getStatusCode()
                + ", Response: " + response.asString());

        log.info("Generated order ID: {}", orderId);

        context.put(ORDER_ID_KEY, orderId);
    }

    @But("order id is empty")
    public void orderIdIsEmpty() {
        context.put(ORDER_ID_KEY, StringUtils.EMPTY);
    }

    @Given("I have the following request parameters")
    public void iHaveTheFollowingRequestParameters(Map<String, String> parameters) {
        parameters.forEach(context::put);
    }

    @Given("I have a {transactionType} request with card details")
    public void iHaveARequestWithCardDetails(TransactionType transactionType,
        ApiCallRequest apiCallRequest) {
        String applicationName = context.get("applicationName", String.class).orElse("IBS_STAGING");
        String totalAmount = context.get("amount", String.class)
            .orElseThrow(() -> new IllegalStateException("Amount is required for payment request"));
        String currency = context.get("currency", String.class).orElseThrow(
            () -> new IllegalStateException("Currency is required for payment request"));
        String cityCode = context.get("cityCode", String.class).orElse("ITT");
        String officeNumber = context.get("officeNumber", String.class).orElse("4662");
        String orderId = context.get(ORDER_ID_KEY, String.class).orElseThrow(
            () -> new IllegalStateException("Order ID is required for payment request"));
        OrderDetails orderDetails = new OrderDetails(new SOR(cityCode, officeNumber), orderId,
            context.getOrDefault(NPHASE_ID_KEY, PaymentGatewayRequest.generateNPhaseId()),
            transactionType.type(), totalAmount, currency, 0, false);
        ClientBrowserDetails clientBrowserDetails = PaymentGatewayRequest.createDefaultBrowserDetails();
        if (transactionType == TransactionType.TDS_ADDRESS_LOOKUP) {
            apiCallRequest.tdsParameters().parameters().getFirst().setName("okUrl");
            apiCallRequest.tdsParameters().parameters().getFirst()
                .setValue(clientBrowserDetails.returnURL());
            apiCallRequest.tdsParameters().parameters().get(1).setName("failUrl");
            apiCallRequest.tdsParameters().parameters().get(1)
                .setValue(clientBrowserDetails.returnURL().replace("threeDSuccess", "threeDFail"));
        }
        PaymentGatewayRequest request = PaymentGatewayRequest.builder()
            .applicationName(applicationName).clientBrowserDetails(clientBrowserDetails)
            .orderDetails(orderDetails).apiCallRequest(apiCallRequest).build();
        context.put(REQUEST_KEY, request);
    }

    @But("nphase id is empty")
    public void nphaseIdIsEmpty() {
        context.put(NPHASE_ID_KEY, StringUtils.EMPTY);
    }

    @Given("I have passengers")
    public void iHaveTheFollowingPassengerInformation(List<Map<String, String>> passengersData) {
        context.put(PASSENGERS_KEY, IntStream.range(0, passengersData.size()).mapToObj(i -> {
            Map<String, String> data = passengersData.get(i);
            return new Passenger(i + 1, data.get("title"), data.get("type"), data.get("givenName"),
                data.get("familyName"), data.get("emailAddress"), data.get("phoneNumber"));
        }).toList());
    }

    @When("I send a {transactionType} request with card details")
    public void iSendATdsAddressLookupRequestWithCardDetails(TransactionType transactionType,
        ApiCallRequest apiCallRequest) {
        iHaveARequestWithCardDetails(transactionType, apiCallRequest);
        Response response = paymentGatewayService.performPaymentRequest(context.get(REQUEST_KEY));
        context.put(RESPONSE_KEY, response);
    }

    @Then("I should receive a response with status code {int}")
    public void iShouldReceiveAResponseWithStatusCode(int expectedStatusCode) {
        getResponse().then().statusCode(expectedStatusCode);
    }

    @Then("the response should have XML element {string}")
    public void theResponseShouldHaveXmlElement(String elementPath) {
        getResponse().then().parser("", Parser.XML).assertThat()
            .body(elementPath + ".size()", greaterThan(0));
    }

    @Then("the response should not have XML element {string}")
    public void theResponseShouldNotHaveXmlElement(String elementPath) {
        getResponse().then().parser("", Parser.XML).assertThat()
            .body(elementPath + ".size()", equalTo(0));
    }

    @Then("the response XML element {string} value should be {string}")
    public void theResponseXMLElementValueShouldBe(String elementPath, String expectedValue) {
        getResponse().then().parser("", Parser.XML).body(elementPath, equalTo(expectedValue));
    }

    @Then("the response XML element {string} value should start with {string}")
    public void theResponseXMLElementValueShouldStartWith(String elementPath,
        String expectedValue) {
        getResponse().then().parser("", Parser.XML).body(elementPath, startsWith(expectedValue));
    }

    @Then("the response XML element {string} value should be {contextPropertyValue}")
    public void theResponseXMLElementValueShouldBeContextValue(String elementPath,
        String expectedValue) {
        theResponseXMLElementValueShouldBe(elementPath, expectedValue);
    }

    private Response getResponse() {
        return context.get(RESPONSE_KEY, Response.class)
            .orElseThrow(() -> new IllegalStateException("No API response found in context"));
    }
}
