package unit;

import org.junit.jupiter.api.Test;
import pg.api.automation.model.component.ApiCallRequest;
import pg.api.automation.util.XmlUtils;

class XmlUtilsTest {

    @Test
    void test_object_deeply_empty() {
        String xml = XmlUtils.toXml(new ApiCallRequest("s", "s", "c", "d"));
        System.out.println(xml);
        assert xml != null;
        assert xml.contains("<App2PGServiceRequestMessage");
        assert xml.contains("xmlns=\"schema.pg.thy.com\"");
    }

}
