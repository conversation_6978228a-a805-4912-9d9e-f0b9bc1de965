<?xml version="1.0" encoding="UTF-8" ?>

<configuration>
  <include resource="org/springframework/boot/logging/logback/default.xml"/>
  <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

  <conversionRule conversionWord="htmlEncode"
    converterClass="pg.api.automation.layout.HtmlContentEncoderMessageConverter"/>

  <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%highlight(%d{HH:mm:ss.SSS} [%t] %-5level) - %msg%n</pattern>
    </encoder>
  </appender>

  <appender name="ReportPortalAppender"
    class="com.epam.reportportal.logback.appender.ReportPortalAppender">
    <encoder>
      <pattern>%htmlEncode{%msg}</pattern>
    </encoder>
  </appender>

  <root level="INFO">
    <appender-ref ref="CONSOLE"/>
    <appender-ref ref="ReportPortalAppender"/>
  </root>

</configuration>