@ODETP-11558
Feature: Payment Gateway Service API Tests
  As a payment system integrator
  I want to perform payment operations (FraudCheck, Sale, TdsAddressLookup)
  So that I can process secure and validated payment transactions

  @ODETP-11561 @tdsAddressLookup @disabled
  Scenario: Validation | TransactionType: TdsAddressLookup parametresinin kontrolü
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 03          | 21         | 899 |
    Then I should receive a response with status code 200
    And the response should not have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.TransactionType" value should be "TdsAddressLookup"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}

  @ODETP-13357 @tdsAddressLookup @currency
  Scenario: Validation | Farkli Currency ile TdsAddressLookup
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | USD         |
    When I send a TdsAddressLookup request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 03          | 21         | 899 |
    Then I should receive a response with status code 200
    And the response should not have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.TransactionType" value should be "TdsAddressLookup"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.Currency" value should be "USD"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}

  @ODETP-11562 @fraudCheck
  Scenario: Validation | TransactionType: FraudCheck parametresinin kontrolü
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a FraudCheck request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 03          | 21         | 899 |
    Then I should receive a response with status code 200
    And the response should not have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.TransactionType" value should be "FraudCheck"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}

  @ODETP-11563 @sale
  Scenario: Validation | TransactionType: Sale parametresinin kontrolü
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a Sale request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | 5610591081018250 | 02          | 30         | 123 |
    Then I should receive a response with status code 200
    And the response should not have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.TransactionType" value should be "Sale"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultCode" value should be "20002"
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultMessage" value should be "CCPayResponseXml Transaction approved"

  @ODETP-12621 @tdsAddressLookup @exception
  Scenario: Validation | CardNumber alaninin bos gönderilmesi
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber | expiryMonth | expiryYear | cvv |
      |            | 03          | 21         | 899 |
    Then I should receive a response with status code 200
    And the response should have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.ErrorResponse.ErrorCode" value should be "80001"
    And the response XML element "PG2AppServiceResponseMessage.ErrorResponse.ErrorMessage" value should be "java.lang.NullPointerExce"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}

  @ODETP-12622 @tdsAddressLookup @exception
  Scenario: Validation | CardNumber alaninin hatali gönderilmesi
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber      | expiryMonth | expiryYear | cvv  |
      | *************** | 12          | 29         | 1233 |
    Then I should receive a response with status code 200
    And the response should have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.ErrorResponse.ErrorCode" value should be "20001"
    And the response XML element "PG2AppServiceResponseMessage.ErrorResponse.ErrorMessage" value should be "CCPayResponseXml Transaction declined"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}
    And the response XML element "PG2AppServiceResponseMessage.PspResponse.ErrorResponse.ErrorMessage" value should be "VALUE **************** OF FIELD CREDITCARDNUMBER DID NOT PASS THE LUHNCHECK"

  @ODETP-12650 @tdsAddressLookup @exception
  Scenario: Validation | WorldPay - CardNumber alaninin eksik gönderilmesi
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber      | expiryMonth | expiryYear | cvv |
      | 545454545454545 | 12          | 26         | 123 |
    Then I should receive a response with status code 200
    And the response should have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.PspResponse.ErrorResponse.ErrorCode" value should be "7"
    And the response XML element "PG2AppServiceResponseMessage.PspResponse.ErrorResponse.ErrorMessage" value should start with "Invalid payment details : Check sum failed"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}

  @ODETP-12651 @tdsAddressLookup @exception @worldPay
  Scenario: Validation | WorldPay - ExpDateMonth alaninin bos gönderilmesi
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** |             | 26         | 123 |
    Then I should receive a response with status code 200
    And the response should have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.PspResponse.ErrorResponse.ErrorCode" value should be "2"
    And the response XML element "PG2AppServiceResponseMessage.PspResponse.ErrorResponse.ErrorMessage" value should be "Attribute \"month\" is required and must be specifie"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}

  @ODETP-12652 @tdsAddressLookup @exception
  Scenario: Validation | Validation | ExpDateMonth alaninin hatali gönderilmesi
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber      | expiryMonth | expiryYear | cvv  |
      | *************** | 01          | 29         | 1233 |
    Then I should receive a response with status code 200
    And the response should have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.ErrorResponse.ErrorCode" value should be "80001"
    And the response XML element "PG2AppServiceResponseMessage.ErrorResponse.ErrorMessage" value should be "java.lang.NullPointerExce"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}

  @ODETP-12653 @tdsAddressLookup @exception
  Scenario: Validation | ExpDateMonth alaninin eksik gönderilmesi
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber      | expiryMonth | expiryYear | cvv  |
      | *************** | 2           | 29         | 1233 |
    Then I should receive a response with status code 200
    And the response should have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.ErrorResponse.ErrorCode" value should be "80001"
    And the response XML element "PG2AppServiceResponseMessage.ErrorResponse.ErrorMessage" value should be "java.lang.NullPointerExce"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}

  @ODETP-12654 @tdsAddressLookup @exception @worldPay
  Scenario: Validation | WorldPay - ExpDateYear alaninin bos gönderilmesi
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 12          |            | 123 |
    Then I should receive a response with status code 200
    And the response should have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.PspResponse.ErrorResponse.ErrorCode" value should be "2"
    And the response XML element "PG2AppServiceResponseMessage.PspResponse.ErrorResponse.ErrorMessage" value should be "Attribute value \"\" of type NMTOKEN must be a name "
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}

  @ODETP-12655 @tdsAddressLookup @exception @worldPay
  Scenario: Validation | WorldPay - ExpDateYear alaninin expired gönderilmesi
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 12          | 24         | 123 |
    Then I should receive a response with status code 200
    And the response should have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.PspResponse.ErrorResponse.ErrorCode" value should be "7"
    And the response XML element "PG2AppServiceResponseMessage.PspResponse.ErrorResponse.ErrorMessage" value should start with "Invalid payment details : Expiry date"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}

  @ODETP-12656 @tdsAddressLookup @exception @worldPay
  Scenario: Validation | WorldPay - ExpDateYear alaninin eksik gönderilmesi
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 12          | 9          | 123 |
    Then I should receive a response with status code 200
    And the response should have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.PspResponse.ErrorResponse.ErrorCode" value should be "2"
    And the response XML element "PG2AppServiceResponseMessage.PspResponse.ErrorResponse.ErrorMessage" value should start with "Invalid year"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}

  @ODETP-12657 @tdsAddressLookup @exception
  Scenario: Validation | Orderid alaninin bos gönderilmesi
    Given I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    But order id is empty
    When I send a TdsAddressLookup request with card details
      | cardNumber      | expiryMonth | expiryYear | cvv  |
      | *************** | 12          | 29         | 1233 |
    Then I should receive a response with status code 200
    And the response should have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.ErrorResponse.ErrorCode" value should be "80032"
    And the response XML element "PG2AppServiceResponseMessage.ErrorResponse.ErrorMessage" value should be "Sql exception occured"

  @ODETP-12658 @tdsAddressLookup @exception @disabled
  Scenario: Validation | NPhaseId alaninin bos gönderilmesi
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    But nphase id is empty
    When I send a TdsAddressLookup request with card details
      | cardNumber      | expiryMonth | expiryYear | cvv  |
      | *************** | 12          | 29         | 1233 |
    Then I should receive a response with status code 200
    And the response should have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.ErrorResponse.ErrorCode" value should be "80032"
    And the response XML element "PG2AppServiceResponseMessage.ErrorResponse.ErrorMessage" value should be "Sql exception occured"

  @ODETP-13372 @fraudCheck @tdsAddressLookup @sale @masterCard
  Scenario: IBS_STAGING | Issue | MasterCard - TRY
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 12          | 30         | 000 |
    And I send a FraudCheck request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 12          | 30         | 000 |
    And I send a Sale request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 12          | 30         | 000 |
    Then I should receive a response with status code 200
    And the response should not have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.TransactionType" value should be "Sale"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultCode" value should be "20002"
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultMessage" value should be "CCPayResponseXml Transaction approved"

  @ODETP-13373 @fraudCheck @tdsAddressLookup @sale @visa
  Scenario: IBS_STAGING | Issue | Visa - TRY
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 02          | 30         | 123 |
    And I send a FraudCheck request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 02          | 30         | 123 |
    And I send a Sale request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 02          | 30         | 123 |
    Then I should receive a response with status code 200
    And the response should not have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.TransactionType" value should be "Sale"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultCode" value should be "20002"
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultMessage" value should be "CCPayResponseXml Transaction approved"

  @ODETP-13374 @fraudCheck @tdsAddressLookup @sale @amex
  Scenario: IBS_STAGING | Issue | Amex - TRY
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber      | expiryMonth | expiryYear | cvv  |
      | *************** | 12          | 29         | 1233 |
    And I send a FraudCheck request with card details
      | cardNumber      | expiryMonth | expiryYear | cvv  |
      | *************** | 12          | 29         | 1233 |
    And I send a Sale request with card details
      | cardNumber      | expiryMonth | expiryYear | cvv  |
      | *************** | 12          | 29         | 1233 |
    Then I should receive a response with status code 200
    And the response should not have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.TransactionType" value should be "Sale"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultCode" value should be "20002"
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultMessage" value should be "CCPayResponseXml Transaction approved"