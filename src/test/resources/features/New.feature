@ODETP-11558
Feature: Payment Gateway Service API Tests
  As a payment system integrator
  I want to perform payment operations (FraudCheck, Sale, TdsAddressLookup)
  So that I can process secure and validated payment transactions

  @ODETP-13372 @fraudCheck @tdsAddressLookup @sale @masterCard
  Scenario: IBS_STAGING | Issue | MasterCard - TRY
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 12          | 30         | 000 |
    And I send a FraudCheck request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 12          | 30         | 000 |
    And I send a Sale request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 12          | 30         | 000 |
    Then I should receive a response with status code 200
    And the response should not have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.TransactionType" value should be "Sale"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultCode" value should be "20002"
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultMessage" value should be "CCPayResponseXml Transaction approved"

  @ODETP-13373 @fraudCheck @tdsAddressLookup @sale @visa
  Scenario: IBS_STAGING | Issue | Visa - TRY
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 02          | 30         | 123 |
    And I send a FraudCheck request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 02          | 30         | 123 |
    And I send a Sale request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 02          | 30         | 123 |
    Then I should receive a response with status code 200
    And the response should not have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.TransactionType" value should be "Sale"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultCode" value should be "20002"
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultMessage" value should be "CCPayResponseXml Transaction approved"

  @ODETP-13374 @fraudCheck @tdsAddressLookup @sale @amex
  Scenario: IBS_STAGING | Issue | Amex - TRY
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber      | expiryMonth | expiryYear | cvv  |
      | *************** | 12          | 29         | 1233 |
    And I send a FraudCheck request with card details
      | cardNumber      | expiryMonth | expiryYear | cvv  |
      | *************** | 12          | 29         | 1233 |
    And I send a Sale request with card details
      | cardNumber      | expiryMonth | expiryYear | cvv  |
      | *************** | 12          | 29         | 1233 |
    Then I should receive a response with status code 200
    And the response should not have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.TransactionType" value should be "Sale"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultCode" value should be "20002"
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultMessage" value should be "CCPayResponseXml Transaction approved"

  @ODETP-13375 @fraudCheck @tdsAddressLookup @sale @unionpay
  Scenario: IBS_STAGING | Issue | Unionpay - TRY
    #6223164991230014	12 33 123
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 5256.50     |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | 6223164991230014 | 12          | 33         | 123 |
    And I send a FraudCheck request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | 6223164991230014 | 12          | 33         | 123 |
    And I send a Sale request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | 6223164991230014 | 12          | 33         | 123 |
    Then I should receive a response with status code 200
    And the response should not have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.TransactionType" value should be "Sale"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultCode" value should be "20002"
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultMessage" value should be "CCPayResponseXml Transaction approved"

  @ODETP-13382 @fraudCheck @tdsAddressLookup @sale
  Scenario: IBS_STAGING | Issue | Isbank - TRY
    Given I generate an order ID by application name "IBS_STAGING"
    And I have the following request parameters
      | applicationName | IBS_STAGING |
      | amount          | 10          |
      | currency        | TRY         |
    When I send a TdsAddressLookup request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 12          | 30         | 000 |
    And I send a FraudCheck request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 12          | 30         | 000 |
    And I send a Sale request with card details
      | cardNumber       | expiryMonth | expiryYear | cvv |
      | **************** | 12          | 30         | 000 |
    Then I should receive a response with status code 200
    And the response should not have XML element "PG2AppServiceResponseMessage.ErrorResponse"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.TransactionType" value should be "Sale"
    And the response XML element "PG2AppServiceResponseMessage.OrderDetails.OrderID" value should be ${context:orderId}
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultCode" value should be "20002"
    And the response XML element "PG2AppServiceResponseMessage.TrnxResult.TrnxResultMessage" value should be "CCPayResponseXml Transaction approved"