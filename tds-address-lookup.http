### TdsAddressLookup API Request
# This request performs a TdsAddressLookup operation to validate address information for 3D Secure transactions

# Base URL can be changed based on environment (default: kurumsaltest)
@baseUrl = https://kurumsaltest.thy.com
@endpoint = /PaymentGateway/apiCallPayment.do

# Send TdsAddressLookup request
POST {{baseUrl}}{{endpoint}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="PG_XML"

<App2PGServiceRequestMessage xmlns="schema.pg.thy.com">
  <Language>en</Language>
  <ApplicationName>IBS_STAGING</ApplicationName>
  <ClientApplicationVersion>CHROME13 ,*********</ClientApplicationVersion>
  <SubChannel>WEB3</SubChannel>
  <ClientCode>WEB3</ClientCode>
  <ClientIP>*************</ClientIP>
  <ClientOS>Windows</ClientOS>
  <DeviceType>Computer</DeviceType>
  <ClientIpCountry>TR</ClientIpCountry>
  <ClientIpCity>Istanbul</ClientIpCity>
  <ClientBrowserDetails>
    <SessionID>8819.572975477327</SessionID>
    <AcceptHeader>application/json</AcceptHeader>
    <UserAgentHeader>Computer, Microsoft Corporation, Windows, CHROME13, *********</UserAgentHeader>
    <ReturnURL>https://nuat.turkishairlines.com/com.thy.web.online.ibs/ibs/payment/threeDSuccess</ReturnURL>
    <WindowSize>250x400</WindowSize>
    <TimezoneOffSet>0</TimezoneOffSet>
    <ColorDepth>0</ColorDepth>
    <JavaEnabled>false</JavaEnabled>
    <Locale>en_XX</Locale>
  </ClientBrowserDetails>
  <OrderDetails>
    <SOR>
      <CityCode>ITT</CityCode>
      <OfficeNumber>4662</OfficeNumber>
    </SOR>
    <OrderID>E3BNKP</OrderID>
    <NPhaseId>20250426200038047044</NPhaseId>
    <TransactionType>TdsAddressLookup</TransactionType>
    <TotalAmount>467730.96</TotalAmount>
    <Currency>TRY</Currency>
    <NumberOfInstallment>0</NumberOfInstallment>
    <multiplePayment>false</multiplePayment>
  </OrderDetails>
  <ApiCallRequest>
    <CardNumber>***************</CardNumber>
    <ecomPaymentCardExpDateMonth>12</ecomPaymentCardExpDateMonth>
    <ecomPaymentCardExpDateYear>29</ecomPaymentCardExpDateYear>
    <Cvv>1233</Cvv>
    <TDSParameters>
      <extraParameters>
        <Parameter>
          <Name>okUrl</Name>
          <Value>https://nuat.turkishairlines.com/com.thy.web.online.ibs/ibs/payment/threeDSuccess</Value>
        </Parameter>
        <Parameter>
          <Name>failUrl</Name>
          <Value>https://nuat.turkishairlines.com/com.thy.web.online.ibs/ibs/payment/threeDFail</Value>
        </Parameter>
      </extraParameters>
    </TDSParameters>
  </ApiCallRequest>
  <FraudDetectionInformationRequest>
    <DeviceFingerprintID>171ed92916188bdc474f336f7a948218</DeviceFingerprintID>
    <exec>false</exec>
  </FraudDetectionInformationRequest>
</App2PGServiceRequestMessage>
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### Generate Order ID (Optional - can be used to get a new order ID)
# This request generates a new order ID that can be used in the TdsAddressLookup request

POST {{baseUrl}}/PaymentGateway/generateOrderID.do
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="PG_XML"

<GeneratedOrderIDRequest xmlns="schema.pg.thy.com">
  <ApplicationName>IBS_STAGING</ApplicationName>
</GeneratedOrderIDRequest>
------WebKitFormBoundary7MA4YWxkTrZu0gW--