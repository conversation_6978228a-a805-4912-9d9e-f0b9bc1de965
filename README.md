# Payment Gateway API Test Automation

This project provides automated tests for the Payment Gateway API, specifically focusing on the TdsAddressLookup, FraudCheck, and Sale operations.

## Technology Stack

- Java 17
- Spring Boot 3.2.3
- Cucumber 7.15.0 (BDD framework)
- JUnit 5 (Test runner)
- REST Assured (API testing)
- AssertJ (Assertions)
- <PERSON><PERSON> (Build tool)

## Project Structure

The project follows a standard Maven project structure with:

- `src/main/java`: Core functionality and models
- `src/test/java`: Test code, step definitions, and transformers
- `src/test/resources/features`: Cucumber feature files

## Key Components

- **ScenarioRunContext**: Manages state between test steps
- **ApiService**: Generic service for API calls
- **PaymentGatewayService**: Service specialized for payment gateway operations
- **ConsulService**: Service for retrieving flight information from Consul
- **Model Classes**: Request models for different API operations
- **Step Definitions**: Implementation of Cucumber steps
- **Data Transformers**: Convert Cucumber data tables to Java objects

## Running the Tests

### Prerequisites

- Java 17 or higher
- Maven

### Command to Run Tests

```bash
mvn clean test
```

### Test Reports

After running the tests, reports can be found in:

```
target/cucumber-reports/report.html
```

## Feature Files

The project includes three main feature files:

1. **TdsAddressLookup.feature**: Tests for address validation with 3D Secure
2. **FraudCheck.feature**: Tests for fraud detection
3. **Sale.feature**: Tests for payment processing

## Configuration

Configuration is managed through `application.yml` which includes:

- API endpoints and base URLs
- Consul service configuration for flight information
- Test timeouts and retry settings

## Flight Information from Consul

The project now integrates with Consul to automatically retrieve flight information based on flight numbers. This eliminates the need to manually provide all flight details in tests.

### Enhanced Consul Integration

The Consul integration has been enhanced with the following features:

- **In-memory caching**: Flight information is cached to reduce API calls and improve performance
- **Error handling**: Robust error handling with fallback mechanisms when Consul is unavailable
- **Retry mechanism**: Automatic retries for failed API calls with configurable attempts and delays
- **Service discovery**: Ability to check service availability and health

These enhancements make the integration more resilient and performant, especially in environments with network instability or high latency.

### Configuration Options

The Consul integration can be configured in `application.yml`:

```yaml
consul:
  base-url: https://consulqa.example.com
  paths:
    flight-info: /api/flight-info
    service-health: /v1/health/service
    service-catalog: /v1/catalog/service
    status: /v1/status/leader
  cache:
    enabled: true                # Enable/disable caching
    expiration: 3600000          # Cache expiration in milliseconds
  retry:
    enabled: true                # Enable/disable retry mechanism
    max-attempts: 3              # Maximum retry attempts
    delay: 1000                  # Delay between retries in milliseconds
```

### Using Flight Numbers in Tests

Instead of providing detailed flight segment information, you can now use these step definitions:

```gherkin
Given I have flight number "TK1234"
```

or for multiple flights:

```gherkin
Given I have the following flight numbers
  | TK1234 |
  | TK5678 |
```

The system will automatically fetch all flight details (origin, destination, times, etc.) from Consul, with fallback to default values if the service is unavailable.

## Extending the Project

To add new tests:

1. Create a new feature file in `src/test/resources/features`
2. Implement any new step definitions in `src/test/java/com/example/pgapitestauto/steps`
3. Add any required model classes or transformers

For new API operations, extend the appropriate service classes.
