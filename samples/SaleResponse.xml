<PG2AppServiceResponseMessage xmlns="schema.pg.thy.com">
  <PaymentGatewayVersion>*********</PaymentGatewayVersion>
  <HostName>preprod 2</HostName>
  <OrderDetails>
    <SOR>
      <CityCode>ITT</CityCode>
      <OfficeNumber>4662</OfficeNumber>
    </SOR>
    <TrnxID>000011067095</TrnxID>
    <OrderID>E3BNKP</OrderID>
    <NPhaseId>20250426200038047044</NPhaseId>
    <TransactionType>Sale</TransactionType>
    <TotalAmount>467730.96</TotalAmount>
    <Currency>TRY</Currency>
    <NumberOfInstallment>0</NumberOfInstallment>
    <MerchantId>************</MerchantId>
    <multiplePayment>false</multiplePayment>
  </OrderDetails>
  <TrnxResult>
    <TrnxResultCode>20002</TrnxResultCode>
    <TrnxResultMessage>CCPayResponseXml Transaction approved</TrnxResultMessage>
  </TrnxResult>
  <TrnxHistoryRecord>
    <PspTransactionHistoryRecordsList/>
  </TrnxHistoryRecord>
  <PspResponse>
    <PspID>IS</PspID>
    <PspDescription>ISBANK</PspDescription>
    <TrnxResult>
      <TrnxResultCode>00</TrnxResultCode>
      <TrnxResultMessage>Approved</TrnxResultMessage>
    </TrnxResult>
    <PspAuthCode>8VURHA</PspAuthCode>
    <CardHolderName>te** te**</CardHolderName>
    <AVSAPPROVALINFORMATION>G</AVSAPPROVALINFORMATION>
  </PspResponse>
  <HostDate>**************</HostDate>
  <CCInfoResponse>
    <CardNumber>561059******8250</CardNumber>
    <CardType>MS</CardType>
    <CardBrandName>Master Card</CardBrandName>
    <ecomPaymentCardExpDateMonth>**</ecomPaymentCardExpDateMonth>
    <ecomPaymentCardExpDateYear>**</ecomPaymentCardExpDateYear>
    <Cvv>***</Cvv>
    <Bin>********</Bin>
  </CCInfoResponse>
  <FraudDetectionInformationResponse>
    <Decision>ACCEPT</Decision>
    <ReasonCode>-1</ReasonCode>
    <FraudFightResponse>
      <ResponseType>ALTERNATIVE_POS</ResponseType>
      <ResponseValue>REDIRECT</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>TKFF ML</ResponseType>
      <ResponseValue>TRUE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>MILFRAUD_SCORE</ResponseType>
      <ResponseValue>TRUE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>CardSubmission</ResponseType>
      <ResponseValue>OPTIONAL</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>OTP</ResponseType>
      <ResponseValue>FALSE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>Action</ResponseType>
      <ResponseValue>ACCEPT</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>3D</ResponseType>
      <ResponseValue>OPTIONAL</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>CyberSource</ResponseType>
      <ResponseValue>FALSE</ResponseValue>
    </FraudFightResponse>
  </FraudDetectionInformationResponse>
</PG2AppServiceResponseMessage>