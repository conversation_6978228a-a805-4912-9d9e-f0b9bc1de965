<PG2AppServiceResponseMessage xmlns="schema.pg.thy.com">
  <PaymentGatewayVersion>*********</PaymentGatewayVersion>
  <HostName>preprod 2</HostName>
  <OrderDetails>
    <SOR>
      <CityCode>ITT</CityCode>
      <OfficeNumber>4662</OfficeNumber>
    </SOR>
    <OrderID>E3BNKP</OrderID>
    <NPhaseId>20250426200038047044</NPhaseId>
    <TransactionType>FraudCheck</TransactionType>
    <TotalAmount>467730.96</TotalAmount>
    <Currency>TRY</Currency>
    <NumberOfInstallment>0</NumberOfInstallment>
    <multiplePayment>false</multiplePayment>
  </OrderDetails>
  <CCInfoResponse>
    <CardType>MS</CardType>
    <CardBrandName>Master Card</CardBrandName>
    <Bin>56105910</Bin>
  </CCInfoResponse>
  <FraudDetectionInformationResponse>
    <Decision>ACCEPT</Decision>
    <ReasonCode>-1</ReasonCode>
    <FraudFightResponse>
      <ResponseType>3DAUTH</ResponseType>
      <ResponseValue>SUCCESS</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>ALTERNATIVE_POS</ResponseType>
      <ResponseValue>REDIRECT</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>TKFF ML</ResponseType>
      <ResponseValue>TRUE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>MILFRAUD_SCORE</ResponseType>
      <ResponseValue>TRUE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>CardSubmission</ResponseType>
      <ResponseValue>OPTIONAL</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>OTP</ResponseType>
      <ResponseValue>FALSE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>Action</ResponseType>
      <ResponseValue>ACCEPT</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>3D</ResponseType>
      <ResponseValue>OPTIONAL</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>CyberSource</ResponseType>
      <ResponseValue>FALSE</ResponseValue>
    </FraudFightResponse>
  </FraudDetectionInformationResponse>
</PG2AppServiceResponseMessage>