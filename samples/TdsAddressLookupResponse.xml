<PG2AppServiceResponseMessage xmlns="schema.pg.thy.com">
  <PaymentGatewayVersion>*********-5c9f253</PaymentGatewayVersion>
  <HostName>musteridev01</HostName>
  <OrderDetails>
    <SOR>
      <CityCode>ITT</CityCode>
      <OfficeNumber>4662</OfficeNumber>
    </SOR>
    <OrderID>E3BVGZ</OrderID>
    <NPhaseId>20250430110516056610</NPhaseId>
    <TransactionType>TdsAddressLookup</TransactionType>
    <PaymentReason>Ticketing</PaymentReason>
    <TotalAmount>8164.32</TotalAmount>
    <Currency>TRY</Currency>
    <checkOut>Normal</checkOut>
    <UserInitial>00</UserInitial>
    <OfferId>a6f9cb0e-72b4-4b32-9d87-b3496c5f5cac</OfferId>
  </OrderDetails>
  <TrnxResult>
    <TrnxResultCode>20002</TrnxResultCode>
    <TrnxResultMessage>CCPayResponseXml Transaction approved</TrnxResultMessage>
  </TrnxResult>
  <FraudDetectionInformationResponse>
    <Decision>ACCEPT</Decision>
    <ReasonCode>-1</ReasonCode>
    <FraudFightResponse>
      <ResponseType>CyberSource</ResponseType>
      <ResponseValue>FALSE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>CardSubmission</ResponseType>
      <ResponseValue>OPTIONAL</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>OTP</ResponseType>
      <ResponseValue>FALSE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>Action</ResponseType>
      <ResponseValue>ACCEPT</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>3D</ResponseType>
      <ResponseValue>OPTIONAL</ResponseValue>
    </FraudFightResponse>
  </FraudDetectionInformationResponse>
  <FraudDetectionInformationResponse>
    <Decision>ACCEPT</Decision>
    <FraudFightResponse>
      <ResponseType>ALTERNATIVE_POS</ResponseType>
      <ResponseValue>REDIRECT</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>3D</ResponseType>
      <ResponseValue>OPTIONAL</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>Action</ResponseType>
      <ResponseValue>ACCEPT</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>CyberSource</ResponseType>
      <ResponseValue>FALSE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>CardSubmission</ResponseType>
      <ResponseValue>OPTIONAL</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>TKFF ML</ResponseType>
      <ResponseValue>TRUE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>OTP</ResponseType>
      <ResponseValue>FALSE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>MILFRAUD_SCORE</ResponseType>
      <ResponseValue>TRUE</ResponseValue>
    </FraudFightResponse>
  </FraudDetectionInformationResponse>
  <PaymentOrderResponseList>
    <PaymentOrderResponse>
      <TrnxResult>
        <TrnxResultCode>20002</TrnxResultCode>
        <TrnxResultMessage>CCPayResponseXml Transaction approved</TrnxResultMessage>
      </TrnxResult>
      <CCInfoResponse>
        <CardNumber>561059******8250</CardNumber>
        <CardType>MS</CardType>
        <CardBrandName>Master Card</CardBrandName>
        <Bin>561059</Bin>
      </CCInfoResponse>
      <TDSConfirmationChannel>
        <PaymentServiceProvider>ISBANK</PaymentServiceProvider>
        <PlaceOfOrigin>Local</PlaceOfOrigin>
        <Country>TR</Country>
        <StoreType>3D</StoreType>
        <StoreKey>TEST3232</StoreKey>
        <ChannelUrl>https://kurumsaltest.thy.com/TKBank/Isbank3DSecure.do</ChannelUrl>
      </TDSConfirmationChannel>
      <PaymentOrderDetail>
        <PaymentIndex>1</PaymentIndex>
        <SubOrderId>E3BVH0</SubOrderId>
        <TotalAmount>6209.91</TotalAmount>
        <Currency>TRY</Currency>
        <CurrencyIso>949</CurrencyIso>
        <NumberOfInstallment>1</NumberOfInstallment>
        <SupplierId>1</SupplierId>
        <MerchantId>************</MerchantId>
      </PaymentOrderDetail>
    </PaymentOrderResponse>
    <PaymentOrderResponse>
      <ErrorResponse>
        <ErrorCode>30010</ErrorCode>
        <ErrorMessage>PSP doesnt support tds request.</ErrorMessage>
      </ErrorResponse>
      <TrnxResult>
        <TrnxResultCode>20001</TrnxResultCode>
        <TrnxResultMessage>CCPayResponseXml Transaction declined</TrnxResultMessage>
      </TrnxResult>
      <PaymentOrderDetail>
        <PaymentIndex>2</PaymentIndex>
        <SubOrderId>E3BVH1</SubOrderId>
        <TotalAmount>6209.91</TotalAmount>
        <Currency>TRY</Currency>
      </PaymentOrderDetail>
    </PaymentOrderResponse>
  </PaymentOrderResponseList>
</PG2AppServiceResponseMessage>