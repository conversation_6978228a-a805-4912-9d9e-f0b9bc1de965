<App2PGServiceRequestMessage xmlns="schema.pg.thy.com">
  <Language>en</Language>
  <ApplicationName>IBS_STAGING</ApplicationName>
  <ClientApplicationVersion>CHROME13 ,*********</ClientApplicationVersion>
  <SubChannel>WEB3</SubChannel>
  <ClientCode>WEB3</ClientCode>
  <ClientIP>*************</ClientIP>
  <ClientOS>Windows</ClientOS>
  <DeviceType>Computer</DeviceType>
  <ClientIpCountry>TR</ClientIpCountry>
  <ClientIpCity>Istanbul</ClientIpCity>
  <OrderDetails>
    <SOR>
      <CityCode>ITT</CityCode>
      <OfficeNumber>4662</OfficeNumber>
    </SOR>
    <OrderID>E3BNKP</OrderID>
    <NPhaseId>20250426200038047044</NPhaseId>
    <TransactionType>FraudCheck</TransactionType>
    <TotalAmount>467730.96</TotalAmount>
    <Currency>TRY</Currency>
    <NumberOfInstallment>0</NumberOfInstallment>
    <multiplePayment>false</multiplePayment>
  </OrderDetails>
  <ApiCallRequest>
    <CardNumber>561059RMMBM18250</CardNumber>
    <ecomPaymentCardExpDateMonth>***</ecomPaymentCardExpDateMonth>
    <ecomPaymentCardExpDateYear>***</ecomPaymentCardExpDateYear>
    <Cvv>***</Cvv>
  </ApiCallRequest>
  <FraudDetectionInformationRequest>
    <DeviceFingerprintID>171ed92916188bdc474f336f7a948218</DeviceFingerprintID>
    <exec>false</exec>
  </FraudDetectionInformationRequest>
  <TravelInformatonDetails>
    <OriginCountry2Digits>AE</OriginCountry2Digits>
    <DestinationCountry2Digits>AE</DestinationCountry2Digits>
    <JourneyType>RoundTrip</JourneyType>
    <FlightType>International</FlightType>
    <ThrdPartyBooking>true</ThrdPartyBooking>
    <EtktIndicator>false</EtktIndicator>
    <PnrNumber>RCNTC6</PnrNumber>
    <PnrCreationDate>20250426</PnrCreationDate>
    <SalesOfficeInformation>
      <OfficeNumber>4662</OfficeNumber>
    </SalesOfficeInformation>
    <CardHolder>
      <GivenName>Cihan</GivenName>
      <FamilyName>al</FamilyName>
      <EmailAddress><EMAIL></EmailAddress>
      <PhoneNumber>905346890000</PhoneNumber>
      <StreetAddressA>1295 CHARLESTON</StreetAddressA>
      <StreetAddressB>RD</StreetAddressB>
      <City>MOUNTAIN VIEW</City>
      <State>CA</State>
      <PostalCode>94043</PostalCode>
      <CountryCode>US</CountryCode>
      <CountryISO3166numeric>USA</CountryISO3166numeric>
    </CardHolder>
    <PassengerList>
      <Passenger>
        <Index>1</Index>
        <Title>MR</Title>
        <Type>Adult</Type>
        <GivenName>CIHAN</GivenName>
        <FamilyName>AL</FamilyName>
        <EmailAddress/>
        <PhoneNumber/>
      </Passenger>
      <Passenger>
        <Index>2</Index>
        <Title>MR</Title>
        <Type>Adult</Type>
        <GivenName>TEST</GivenName>
        <FamilyName>AL</FamilyName>
        <EmailAddress/>
        <PhoneNumber/>
      </Passenger>
    </PassengerList>
    <SegmentList>
      <Segment>
        <Index>1</Index>
        <originCountryCode>AE</originCountryCode>
        <destinationCountryCode>TR</destinationCountryCode>
        <OriginAirport>DXB</OriginAirport>
        <OriginAirportGmt>4:00</OriginAirportGmt>
        <DestinationAirport>IST</DestinationAirport>
        <Carrier>TK</Carrier>
        <FlightNumber>759</FlightNumber>
        <FlightDate>20250515</FlightDate>
        <FlightDepartureTime>10:30</FlightDepartureTime>
        <FlightArrivalDate>20250515</FlightArrivalDate>
        <FlightArrivalTime>14:35</FlightArrivalTime>
        <ClassCode>Z</ClassCode>
        <Cabin>Business</Cabin>
        <FareBasis>Z</FareBasis>
        <SegmentStatus>NON-STOP</SegmentStatus>
      </Segment>
      <Segment>
        <Index>2</Index>
        <originCountryCode>TR</originCountryCode>
        <destinationCountryCode>US</destinationCountryCode>
        <OriginAirport>IST</OriginAirport>
        <OriginAirportGmt>3:00</OriginAirportGmt>
        <DestinationAirport>JFK</DestinationAirport>
        <Carrier>TK</Carrier>
        <FlightNumber>1</FlightNumber>
        <FlightDate>20250516</FlightDate>
        <FlightDepartureTime>14:10</FlightDepartureTime>
        <FlightArrivalDate>20250516</FlightArrivalDate>
        <FlightArrivalTime>17:55</FlightArrivalTime>
        <ClassCode>Z</ClassCode>
        <Cabin>Business</Cabin>
        <FareBasis>Z</FareBasis>
      </Segment>
      <Segment>
        <Index>3</Index>
        <originCountryCode>US</originCountryCode>
        <destinationCountryCode>TR</destinationCountryCode>
        <OriginAirport>JFK</OriginAirport>
        <OriginAirportGmt>-4:00</OriginAirportGmt>
        <DestinationAirport>IST</DestinationAirport>
        <Carrier>TK</Carrier>
        <FlightNumber>12</FlightNumber>
        <FlightDate>20250531</FlightDate>
        <FlightDepartureTime>00:35</FlightDepartureTime>
        <FlightArrivalDate>20250531</FlightArrivalDate>
        <FlightArrivalTime>17:15</FlightArrivalTime>
        <ClassCode>Z</ClassCode>
        <Cabin>Business</Cabin>
        <FareBasis>Z</FareBasis>
        <SegmentStatus>NON-STOP</SegmentStatus>
      </Segment>
      <Segment>
        <Index>4</Index>
        <originCountryCode>TR</originCountryCode>
        <destinationCountryCode>AE</destinationCountryCode>
        <OriginAirport>IST</OriginAirport>
        <OriginAirportGmt>3:00</OriginAirportGmt>
        <DestinationAirport>DXB</DestinationAirport>
        <Carrier>TK</Carrier>
        <FlightNumber>760</FlightNumber>
        <FlightDate>20250531</FlightDate>
        <FlightDepartureTime>18:50</FlightDepartureTime>
        <FlightArrivalDate>20250601</FlightArrivalDate>
        <FlightArrivalTime>00:20</FlightArrivalTime>
        <ClassCode>Z</ClassCode>
        <Cabin>Business</Cabin>
        <FareBasis>Z</FareBasis>
      </Segment>
    </SegmentList>
  </TravelInformatonDetails>
</App2PGServiceRequestMessage>