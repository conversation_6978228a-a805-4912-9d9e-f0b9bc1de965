<PG2AppServiceResponseMessage xmlns="schema.pg.thy.com">
  <PaymentGatewayVersion>*********-02df9b0</PaymentGatewayVersion>
  <HostName>musteritest01</HostName>
  <OrderDetails>
    <SOR>
      <CityCode>ITT</CityCode>
      <OfficeNumber>4662</OfficeNumber>
    </SOR>
    <TrnxID>000011131369</TrnxID>
    <OrderID>E3CCNW</OrderID>
    <NPhaseId>20250507213428463624</NPhaseId>
    <TransactionType>TdsAddressLookup</TransactionType>
    <TotalAmount>5256.50</TotalAmount>
    <Currency>TRY</Currency>
    <CurrencyIso>949</CurrencyIso>
    <NumberOfInstallment>0</NumberOfInstallment>
    <MerchantId>56935929</MerchantId>
    <multiplePayment>false</multiplePayment>
  </OrderDetails>
  <ErrorResponse>
    <ErrorCode>20001</ErrorCode>
    <ErrorMessage>CCPayResponseXml Transaction declined</ErrorMessage>
  </ErrorResponse>
  <TrnxResult>
    <TrnxResultCode>20001</TrnxResultCode>
    <TrnxResultMessage>CCPayResponseXml Transaction declined</TrnxResultMessage>
  </TrnxResult>
  <PspResponse>
    <PspID>WN</PspID>
    <PspDescription>WORLDLINE</PspDescription>
    <ErrorResponse>
      <ErrorCode>21000020</ErrorCode>
      <ErrorMessage>VALUE **************** OF FIELD CREDITCARDNUMBER DID NOT PASS THE LUHNCHECK
      </ErrorMessage>
      <PgErrorCode>No Info</PgErrorCode>
      <PgErrorCodeDescription>No Info</PgErrorCodeDescription>
    </ErrorResponse>
    <TrnxResult/>
  </PspResponse>
  <CCInfoResponse>
    <CardNumber>555555******5555</CardNumber>
    <CardType>MS</CardType>
    <CardBrandName>Master Card</CardBrandName>
    <Bin>555555</Bin>
  </CCInfoResponse>
  <TDSConfirmationChannel>
    <PaymentServiceProvider>WORLDLINE</PaymentServiceProvider>
    <PlaceOfOrigin>Local</PlaceOfOrigin>
    <Country>TR</Country>
    <StoreType>3d</StoreType>
    <StoreKey>12345678</StoreKey>
    <ChannelUrl>https://payment.pay1.preprod.secured-by-ingenico.com/redirector</ChannelUrl>
  </TDSConfirmationChannel>
  <FraudDetectionInformationResponse>
    <Decision>ACCEPT</Decision>
    <ReasonCode>-1</ReasonCode>
    <FraudFightResponse>
      <ResponseType>CyberSource</ResponseType>
      <ResponseValue>FALSE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>3D</ResponseType>
      <ResponseValue>MANDATORY</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>TKFF ML</ResponseType>
      <ResponseValue>TRUE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>MILFRAUD_SCORE</ResponseType>
      <ResponseValue>TRUE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>OTP</ResponseType>
      <ResponseValue>FALSE</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>Action</ResponseType>
      <ResponseValue>ACCEPT</ResponseValue>
    </FraudFightResponse>
    <FraudFightResponse>
      <ResponseType>CardSubmission</ResponseType>
      <ResponseValue>OPTIONAL</ResponseValue>
    </FraudFightResponse>
  </FraudDetectionInformationResponse>
</PG2AppServiceResponseMessage>